# 📋 第二步：统一命名规范 - 详细执行计划

## 🎯 总体目标

统一项目中的命名规范，提高代码可读性和维护性，为后续重构奠定基础。

## 📊 当前问题分析

### 🔍 发现的命名问题

1. **组件命名不一致**
   - 部分组件使用PascalCase，部分使用camelCase
   - 文件名与组件名不匹配
   - 导入导出命名不统一

2. **文件命名混乱**
   - 混合使用kebab-case和camelCase
   - 文件夹命名不规范
   - 索引文件命名不统一

3. **变量命名不规范**
   - 函数命名不一致
   - 变量命名含义不明确
   - 常量命名不规范

4. **类型命名问题**
   - TypeScript接口命名不统一
   - 类型别名命名不规范
   - 泛型参数命名不清晰

## 🚀 执行阶段规划

### 阶段1：组件命名统一 (预计30分钟)

#### 🎯 目标
- 统一所有React组件使用PascalCase命名
- 确保文件名与组件名一致
- 统一导入导出命名

#### 📋 具体任务
1. **扫描所有组件文件**
   - 检查 `src/components/` 目录
   - 检查 `src/pages/` 目录
   - 检查其他包含组件的目录

2. **重命名组件**
   - 组件名统一使用PascalCase
   - 文件名与组件名保持一致
   - 更新所有相关的导入语句

3. **验证修改**
   - 运行TypeScript编译检查
   - 启动开发服务器验证
   - 检查页面是否正常显示

#### ✅ 验收标准
- [ ] 所有组件名使用PascalCase
- [ ] 文件名与组件名一致
- [ ] 无TypeScript编译错误
- [ ] 项目正常启动和运行

### 阶段2：文件命名统一 (预计25分钟)

#### 🎯 目标
- 统一文件和文件夹命名规范
- 使用camelCase或kebab-case（根据项目约定）
- 确保命名具有描述性

#### 📋 具体任务
1. **文件夹命名规范**
   - 统一使用kebab-case或camelCase
   - 确保文件夹名具有描述性
   - 更新相关的导入路径

2. **文件命名规范**
   - 组件文件使用PascalCase
   - 工具函数文件使用camelCase
   - 配置文件使用kebab-case
   - 更新所有相关引用

3. **索引文件统一**
   - 统一使用 `index.ts` 或 `index.tsx`
   - 确保导出语句规范
   - 简化导入路径

#### ✅ 验收标准
- [ ] 文件夹命名规范统一
- [ ] 文件命名符合约定
- [ ] 所有导入路径正确
- [ ] 项目编译和运行正常

### 阶段3：变量命名统一 (预计35分钟)

#### 🎯 目标
- 统一函数和变量命名规范
- 提高代码可读性
- 确保命名具有语义化

#### 📋 具体任务
1. **函数命名规范**
   - 使用camelCase命名
   - 动词开头，描述功能
   - 事件处理函数使用handle前缀
   - 布尔值返回函数使用is/has前缀

2. **变量命名规范**
   - 使用camelCase命名
   - 名词性，描述内容
   - 避免缩写和单字母变量
   - 常量使用UPPER_SNAKE_CASE

3. **Hook命名规范**
   - 自定义Hook使用use前缀
   - 状态变量和setter成对命名
   - 确保Hook命名语义化

#### ✅ 验收标准
- [ ] 函数命名规范统一
- [ ] 变量命名具有语义化
- [ ] 常量命名使用大写
- [ ] Hook命名符合React约定

### 阶段4：类型命名统一 (预计20分钟)

#### 🎯 目标
- 统一TypeScript类型定义命名
- 提高类型系统的可读性
- 确保类型命名规范

#### 📋 具体任务
1. **接口命名规范**
   - 使用PascalCase命名
   - 可选择使用I前缀（根据团队约定）
   - 确保接口名具有描述性

2. **类型别名命名**
   - 使用PascalCase命名
   - 可选择使用Type后缀
   - 避免与接口命名冲突

3. **泛型参数命名**
   - 使用单个大写字母（T, U, V等）
   - 或使用描述性名称（TData, TProps等）
   - 确保泛型约束清晰

#### ✅ 验收标准
- [ ] 接口命名规范统一
- [ ] 类型别名命名清晰
- [ ] 泛型参数命名合理
- [ ] 类型系统无错误

## 🔧 技术实施细节

### 重命名策略
1. **批量重命名工具**
   - 使用IDE的重构功能
   - 使用正则表达式批量替换
   - 逐步验证每个修改

2. **导入路径更新**
   - 自动更新相对路径
   - 检查绝对路径引用
   - 验证动态导入语句

3. **Git操作策略**
   - 每个阶段单独提交
   - 使用描述性提交信息
   - 保持提交历史清晰

### 风险控制
1. **备份策略**
   - 每个阶段前创建Git分支
   - 保留原始代码备份
   - 准备快速回滚方案

2. **测试验证**
   - 每个阶段后运行编译检查
   - 启动开发服务器验证
   - 检查关键功能正常

## 📋 执行检查清单

### 开始前检查
- [ ] 确认第一步文件结构重构已完成
- [ ] 项目当前状态正常运行
- [ ] 所有修改已提交到Git
- [ ] 创建新的功能分支

### 每个阶段后检查
- [ ] 运行 `npx tsc --noEmit` 检查类型错误
- [ ] 运行 `pnpm dev` 启动开发服务器
- [ ] 访问主要页面确认功能正常
- [ ] 提交当前阶段的修改

### 完成后检查
- [ ] 所有命名规范统一
- [ ] 项目编译无错误
- [ ] 开发服务器正常启动
- [ ] 关键功能测试通过
- [ ] 文档状态已更新

## 🎯 预期成果

### 直接成果
- 统一的命名规范体系
- 提高的代码可读性
- 减少的理解成本
- 更好的开发体验

### 长期收益
- 降低维护成本
- 提高团队协作效率
- 为后续重构奠定基础
- 提升代码质量标准

## 📝 注意事项

1. **谨慎操作**：每次修改前确保理解影响范围
2. **逐步验证**：不要一次性修改太多文件
3. **保持备份**：随时准备回滚到上一个稳定状态
4. **测试优先**：修改后立即验证功能正常
5. **文档同步**：及时更新相关文档和注释

---

**创建时间**: 2025-01-31  
**状态**: 准备执行  
**预计总耗时**: 110分钟  
**版本**: v1.0
