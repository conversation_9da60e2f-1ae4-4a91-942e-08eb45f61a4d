# 🔧 第二步：统一命名规范 - 代码修改详细说明

## 📋 修改概览

本文档详细说明第二步统一命名规范中需要执行的所有代码修改操作，包括文件重命名、组件重命名、导入路径更新等。

## 🚀 阶段1：文件和文件夹重命名

### 1.1 文件夹重命名操作

```bash
# 1. 修复滑动条组件文件夹名（拼写错误 + 命名规范）
mv app/components/Public_SliderComponet app/components/PublicSliderComponent

# 2. 统一颜色提取相关文件夹命名
mv app/features/colorExtraction/get_color app/features/colorExtraction/getColor
mv app/features/colorExtraction/test_color app/features/colorExtraction/testColor

# 3. 验证重命名结果
ls -la app/components/PublicSliderComponent/
ls -la app/features/colorExtraction/getColor/
ls -la app/features/colorExtraction/testColor/
```

### 1.2 组件文件重命名操作

```bash
# 1. 重命名滑动条组件主文件
mv app/components/PublicSliderComponent/Public_SliderComponet.tsx app/components/PublicSliderComponent/PublicSliderComponent.tsx

# 2. 检查是否有其他需要重命名的文件
find app/ -name "*_*" -type f | grep -E "\.(tsx|ts)$" | head -10
```

## 🔄 阶段2：导入路径批量更新

### 2.1 查找所有需要更新的引用

```bash
# 1. 查找所有引用 Public_SliderComponet 的文件
echo "=== 查找 Public_SliderComponet 引用 ==="
grep -r "Public_SliderComponet" app/ --include="*.tsx" --include="*.ts" -n

# 2. 查找所有引用 get_color 的文件
echo "=== 查找 get_color 引用 ==="
grep -r "get_color" app/ --include="*.tsx" --include="*.ts" -n

# 3. 查找所有引用 test_color 的文件
echo "=== 查找 test_color 引用 ==="
grep -r "test_color" app/ --include="*.tsx" --include="*.ts" -n
```

### 2.2 批量替换导入路径

```bash
# 1. 替换滑动条组件的所有引用
find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' "s|Public_SliderComponet|PublicSliderComponent|g"

# 2. 替换颜色相关的导入路径
find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' "s|/get_color/|/getColor/|g"
find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' "s|/test_color/|/testColor/|g"

# 3. 验证替换结果
echo "=== 验证替换结果 ==="
grep -r "Public_SliderComponet" app/ --include="*.tsx" --include="*.ts" || echo "✅ Public_SliderComponet 已全部替换"
grep -r "get_color" app/ --include="*.tsx" --include="*.ts" | grep -v "getColor" || echo "✅ get_color 已全部替换"
grep -r "test_color" app/ --include="*.tsx" --include="*.ts" | grep -v "testColor" || echo "✅ test_color 已全部替换"
```

## 🎯 阶段3：组件内部命名统一

### 3.1 pcMockupMediaSelector.tsx 组件修改

需要修改的具体内容：

```typescript
// 修改前：
export interface PcMockup_MediaProps {
    // ...
}

export const PcMockup_Media = ({
    // ...
}: PcMockup_MediaProps) => {
    // ...
}

// 修改后：
export interface PcMockupMediaProps {
    // ...
}

export const PcMockupMedia = ({
    // ...
}: PcMockupMediaProps) => {
    // ...
}
```

### 3.2 PublicSliderComponent.tsx 组件检查

检查组件内部命名是否符合规范：

```typescript
// 确保以下命名规范：
// 1. 组件名使用 PascalCase
// 2. 函数名使用 camelCase
// 3. 事件处理函数使用 handle 前缀
// 4. 布尔值函数使用 is/has 前缀
// 5. 常量使用 UPPER_SNAKE_CASE
```

## 🧪 阶段4：编译和测试验证

### 4.1 TypeScript编译检查

```bash
# 1. 清理缓存
rm -rf .next/
rm -rf node_modules/.cache/

# 2. TypeScript编译检查
npx tsc --noEmit

# 3. 如果有错误，显示详细信息
npx tsc --noEmit --pretty
```

### 4.2 开发服务器启动测试

```bash
# 1. 启动开发服务器
pnpm dev

# 2. 检查启动日志是否有错误
# 3. 访问 http://localhost:3000 验证页面正常
```

### 4.3 功能验证清单

```bash
# 创建功能验证脚本
cat > verify-naming-refactor.sh << 'EOF'
#!/bin/bash

echo "🔍 开始验证命名重构结果..."

# 1. 检查文件是否存在
echo "📁 检查重命名后的文件..."
[ -d "app/components/PublicSliderComponent" ] && echo "✅ PublicSliderComponent 文件夹存在" || echo "❌ PublicSliderComponent 文件夹不存在"
[ -f "app/components/PublicSliderComponent/PublicSliderComponent.tsx" ] && echo "✅ PublicSliderComponent.tsx 文件存在" || echo "❌ PublicSliderComponent.tsx 文件不存在"
[ -d "app/features/colorExtraction/getColor" ] && echo "✅ getColor 文件夹存在" || echo "❌ getColor 文件夹不存在"
[ -d "app/features/colorExtraction/testColor" ] && echo "✅ testColor 文件夹存在" || echo "❌ testColor 文件夹不存在"

# 2. 检查旧文件是否已删除
echo "🗑️ 检查旧文件是否已清理..."
[ ! -d "app/components/Public_SliderComponet" ] && echo "✅ 旧的 Public_SliderComponet 文件夹已删除" || echo "❌ 旧的 Public_SliderComponet 文件夹仍存在"
[ ! -d "app/features/colorExtraction/get_color" ] && echo "✅ 旧的 get_color 文件夹已删除" || echo "❌ 旧的 get_color 文件夹仍存在"
[ ! -d "app/features/colorExtraction/test_color" ] && echo "✅ 旧的 test_color 文件夹已删除" || echo "❌ 旧的 test_color 文件夹仍存在"

# 3. 检查导入路径是否更新
echo "🔗 检查导入路径更新..."
OLD_REFS=$(grep -r "Public_SliderComponet" app/ --include="*.tsx" --include="*.ts" | wc -l)
if [ "$OLD_REFS" -eq 0 ]; then
    echo "✅ 所有 Public_SliderComponet 引用已更新"
else
    echo "❌ 仍有 $OLD_REFS 个 Public_SliderComponet 引用未更新"
fi

# 4. TypeScript编译检查
echo "🔧 TypeScript编译检查..."
if npx tsc --noEmit > /dev/null 2>&1; then
    echo "✅ TypeScript编译通过"
else
    echo "❌ TypeScript编译失败"
    npx tsc --noEmit
fi

echo "✨ 验证完成！"
EOF

chmod +x verify-naming-refactor.sh
```

## 📦 Git操作记录

### 提交策略

```bash
# 1. 创建功能分支
git checkout -b feature/naming-refactor

# 2. 阶段性提交
# 阶段1完成后
git add .
git commit -m "feat: 重命名文件夹和组件文件

- 修复 Public_SliderComponet 拼写错误并重命名为 PublicSliderComponent
- 统一颜色相关文件夹命名：get_color -> getColor, test_color -> testColor
- 重命名组件文件以匹配新的命名规范"

# 阶段2完成后
git add .
git commit -m "feat: 更新所有导入路径引用

- 批量替换 Public_SliderComponet 为 PublicSliderComponent
- 更新颜色相关模块的导入路径
- 确保所有文件引用正确的新路径"

# 阶段3完成后
git add .
git commit -m "feat: 统一组件内部命名规范

- 更新组件名和接口名使用 PascalCase
- 统一函数和变量命名规范
- 确保事件处理函数使用 handle 前缀"

# 最终验证完成后
git add .
git commit -m "feat: 完成第二步统一命名规范重构

- 所有文件和组件命名符合团队规范
- TypeScript编译通过
- 应用功能正常运行
- 为后续重构奠定基础"
```

## ⚠️ 注意事项

1. **备份重要**：执行前确保已创建备份分支
2. **逐步验证**：每个阶段完成后立即验证
3. **路径敏感**：特别注意相对路径和绝对路径的区别
4. **缓存清理**：如遇到奇怪问题，先清理 .next 和 node_modules 缓存
5. **IDE重启**：重命名后建议重启IDE以刷新索引

---

**创建时间**: 2025-01-31  
**状态**: 准备执行  
**版本**: v1.0
