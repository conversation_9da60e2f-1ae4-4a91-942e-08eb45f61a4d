# 🚀 第一步：文件结构重构详细执行计划

## 📋 修正说明

根据要求：

1. **使用驼峰命名**：不使用 "-" 而是使用驼峰形式
2. **保留关键字**：保留 "Public"、"Pc"、"Frame"、"Mockup" 等关键字
3. **补充代码修改**：详细说明每个文件移动后需要修改的代码内容

## 🗂️ 修正后的目标目录结构

```
app/
├── components/
│   ├── ui/                     # 基础UI组件
│   ├── layout/                 # 布局组件
│   │   ├── mobileMockupLayout.tsx
│   │   ├── mobileMockupTabs.tsx
│   │   ├── publicMockupSmallLayout.tsx
│   │   └── publicPanelTabs.tsx
│   └── business/               # 业务组件
│       ├── pcMockupMediaSelector.tsx
│       ├── pcMockupVisibilityControl.tsx
│       ├── pcRightSlider.tsx
│       ├── publicMockupDetails.tsx
│       ├── publicMockupStyle.tsx
│       ├── publicMockupShadow.tsx
│       └── modals/
│           ├── pcFrameEffectsModalDefault.tsx
│           ├── pcFrameEffectsModalPortrait.tsx
│           ├── pcFrameEffectsModalVef.tsx
│           ├── pcFrameShapesItemsModal.tsx
│           ├── pcFrameWaterMarkModal.tsx
│           ├── publicFrameColorPickerModal.tsx
│           ├── publicFrameCustomImageModal.tsx
│           ├── publicFrameSizeModal.tsx
│           ├── publicMediaPickerModal.tsx
│           └── publicMockupModal.tsx
├── features/
│   ├── imageManagement/        # 图片管理模块
│   ├── colorExtraction/        # 颜色提取模块
│   ├── deviceMockup/           # 设备模拟模块
│   │   ├── publicMockupModelSelector.tsx
│   │   ├── publicMockupSizeSelector.tsx
│   │   ├── publicFrameScene.tsx
│   │   └── publicFrameCustomView.tsx
│   ├── canvasRendering/        # 画布渲染模块
│   └── exportSystem/           # 导出系统模块
└── shared/
    ├── types/
    ├── utils/
    ├── hooks/
    └── config/
```

## 📋 执行前准备

### 1. Git 分支管理

```bash
# 创建功能分支
git checkout -b feature/文件结构重构
git add .
git commit -m "backup: 文件结构重构前的完整备份"

# 创建标签作为回滚点
git tag -a v重构前备份 -m "文件结构重构前的稳定版本"
```

### 2. 创建新目录结构

```bash
# 创建新的 app 目录结构
mkdir -p app/components/ui
mkdir -p app/components/layout
mkdir -p app/components/business/modals
mkdir -p app/features/imageManagement/core
mkdir -p app/features/imageManagement/components
mkdir -p app/features/imageManagement/docs
mkdir -p app/features/colorExtraction/magicBackground
mkdir -p app/features/colorExtraction/test
mkdir -p app/features/deviceMockup
mkdir -p app/features/canvasRendering/container
mkdir -p app/features/canvasRendering/display
mkdir -p app/features/exportSystem/core
mkdir -p app/shared/types
mkdir -p app/shared/utils
mkdir -p app/shared/hooks
mkdir -p app/shared/config
mkdir -p app/styles/globals
mkdir -p app/styles/components
```

## 🔄 第一阶段：布局组件移动和修改

### 1. MobileMockup_Layout.tsx → mobileMockupLayout.tsx

**文件移动：**

```bash
mv app/MobileMockup_Layout.tsx app/components/layout/mobileMockupLayout.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useMockupStore } from './hooks/useMockupStore'
import './css/globals.scss'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useMockupStore } from '@/shared/hooks/useMockupStore'
import '@/styles/globals/globals.scss'

// 组件导出保持不变
export default function MobileMockupLayout() {
    // 组件内容不变
}
```

**验证步骤：**

```bash
# 检查 TypeScript 编译
npx tsc --noEmit

# 测试相关功能
npm run dev
# 测试移动端布局是否正常显示
```

### 2. MobileMockup_Tabs.tsx → mobileMockupTabs.tsx

**文件移动：**

```bash
mv app/MobileMockup_Tabs.tsx app/components/layout/mobileMockupTabs.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { ColorTypes } from './types/colorTypes'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { ColorTypes } from '@/shared/types/colorTypes'

export default function MobileMockupTabs() {
    // 组件内容不变
}
```

## 🔄 第二阶段：业务组件移动和修改

### 3. PcMockup_Media.tsx → pcMockupMediaSelector.tsx

**文件移动：**

```bash
mv app/PcMockup_Media.tsx app/components/business/pcMockupMediaSelector.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
import PublicMediaPickerModal from './Public_MediaPickerModal'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
import PublicMediaPickerModal from './modals/publicMediaPickerModal'

export default function PcMockupMedia() {
    // 组件内容不变
}
```

### 4. PcRightSlider.tsx → pcRightSlider.tsx

**文件移动：**

```bash
mv app/PcRightSlider.tsx app/components/business/pcRightSlider.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useMockupStore } from './hooks/useMockupStore'
import PcFrameEffectsModalDefault from './PcFrame_Effects__ModalDefault'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useMockupStore } from '@/shared/hooks/useMockupStore'
import PcFrameEffectsModalDefault from './modals/pcFrameEffectsModalDefault'

export default function PcRightSlider() {
    // 组件内容不变
}
```

## 🔄 第三阶段：模态框组件移动和修改

### 5. PcFrame_Effects\_\_ModalDefault.tsx → pcFrameEffectsModalDefault.tsx

**文件移动：**

```bash
mv app/PcFrame_Effects__ModalDefault.tsx app/components/business/modals/pcFrameEffectsModalDefault.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { ColorTypes } from './types/colorTypes'
import { Modal } from './components/Modal'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { ColorTypes } from '@/shared/types/colorTypes'
import { Modal } from '@/components/ui/Modal'

export default function PcFrameEffectsModalDefault() {
    // 组件内容不变
}
```

### 6. Public_MediaPickerModal.tsx → publicMediaPickerModal.tsx

**文件移动：**

```bash
mv app/Public_MediaPickerModal.tsx app/components/business/modals/publicMediaPickerModal.tsx
```

**代码修改内容：**

```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
import { imageExport } from './utils/imageExport'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
import { imageExport } from '@/shared/utils/imageExport'

export default function PublicMediaPickerModal() {
    // 组件内容不变
}
```

## 🔄 第四阶段：主要文件更新

### app/page.tsx 主页面文件修改

**修改前的导入部分：**

```typescript
import MobileMockupLayout from './MobileMockup_Layout'
import MobileMockupTabs from './MobileMockup_Tabs'
import PcMockupMedia from './PcMockup_Media'
import PcRightSlider from './PcRightSlider'
import PublicMediaPickerModal from './Public_MediaPickerModal'
import PcFrameEffectsModalDefault from './PcFrame_Effects__ModalDefault'
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
```

**修改后的导入部分：**

```typescript
import MobileMockupLayout from '@/components/layout/mobileMockupLayout'
import MobileMockupTabs from '@/components/layout/mobileMockupTabs'
import PcMockupMediaSelector from '@/components/business/pcMockupMediaSelector'
import PcRightSlider from '@/components/business/pcRightSlider'
import PublicMediaPickerModal from '@/components/business/modals/publicMediaPickerModal'
import PcFrameEffectsModalDefault from '@/components/business/modals/pcFrameEffectsModalDefault'
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
```

## ✅ 每阶段验证步骤

### 阶段验证清单

- [ ] 文件成功移动到新位置
- [ ] 文件内的导入路径已更新
- [ ] TypeScript 编译无错误：`npx tsc --noEmit`
- [ ] 应用正常启动：`npm run dev`
- [ ] 相关功能测试正常
- [ ] 提交当前阶段变更：`git add . && git commit -m "完成第X阶段文件移动"`

### 最终验证

- [ ] 运行完整的功能测试
- [ ] 运行构建测试：`npm run build`
- [ ] 所有页面和功能正常工作
- [ ] ESLint 检查通过：`npx eslint app/ --ext .ts,.tsx`

## 🚨 回滚计划

### 单阶段回滚

```bash
# 回滚到上一个提交
git reset --hard HEAD~1
```

### 完整回滚

```bash
# 回滚到重构前的标签
git reset --hard v重构前备份
git clean -fd
```

### 紧急回滚

```bash
# 如果出现严重问题，立即回滚到主分支
git checkout main
git branch -D feature/文件结构重构
```

这个计划确保每个步骤都有详细的代码修改说明和验证步骤，你觉得这样的安排如何？

---

## 📋 执行状态更新 (2025-01-31)

- [x] 第一阶段：根目录清理 ✅ 已完成
- [x] 第二阶段：业务组件移动和修改 ✅ 已完成
- [x] 第三阶段：功能模块移动和修改 ✅ 已完成
- [x] 第四阶段：主要文件更新 ✅ 已完成
- [x] 第五阶段：测试和验证 ✅ 已完成
- [x] 问题修复：Zustand状态管理失效 ✅ 已完成
- [x] 最终整理：剩余文件移动 ✅ 已完成
- [x] 导入路径修复：文件移动后的路径问题 ✅ 已完成
- [x] 最终检查：修复剩余导入路径问题 ✅ 已完成

## 🎉 重构完成总结

**重构已成功完成！** 所有计划的目标都已实现：

1. **文件结构重构** ✅ - 建立了清晰的模块化结构
2. **命名规范统一** ✅ - 使用驼峰命名，保持一致性
3. **导入路径优化** ✅ - 统一使用绝对路径别名
4. **功能模块化** ✅ - 按功能分组，便于维护
5. **状态管理修复** ✅ - 解决了Zustand状态管理问题

**验证结果**：

- ✅ 应用程序正常启动
- ✅ 所有功能正常工作
- ✅ TypeScript编译通过
- ✅ 用户功能测试通过

项目现在拥有了清晰、可维护的代码结构，为后续开发奠定了坚实基础。
